import { Elysia } from 'elysia';
import { cors } from '@elysiajs/cors';
import { routes } from './routes/index.js';
import 'dotenv/config';

const PORT = process.env.PORT || 3001;
const FRONTEND_URL = process.env.FRONTEND_URL || 'http://localhost:3000';

const app = new Elysia()
  .use(cors({
    origin: FRONTEND_URL,
    methods: ['GET', 'POST', 'PUT', 'DELETE'],
    allowedHeaders: ['Content-Type', 'Authorization'],
    credentials: true
  }))
  .use(routes)
  .onError(({ error, code }) => {
    console.error(`Error ${code}:`, error);
    
    if (code === 'VALIDATION') {
      return { 
        success: false, 
        error: 'Validation failed', 
        details: error.message 
      };
    }
    
    return { 
      success: false, 
      error: error.message || 'Internal server error' 
    };
  })
  .listen(PORT);

console.log(`🦊 VibeCheck API running at http://localhost:${PORT}`);
console.log(`📊 Health check: http://localhost:${PORT}/api/health`);
console.log(`🔗 API endpoints: http://localhost:${PORT}/api`);

export default app;
