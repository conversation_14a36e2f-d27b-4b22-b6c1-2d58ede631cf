import { Elysia } from 'elysia';
import { cors } from '@elysiajs/cors';
import { swagger } from '@elysiajs/swagger';
import { routes } from './routes/index.js';
import { testConnection, initializeDatabase } from './db/index.js';
import 'dotenv/config';

const PORT = process.env.PORT || 3001;
const FRONTEND_URL = process.env.FRONTEND_URL || 'http://localhost:3000';

// Initialize database before starting server
async function startServer() {
  console.log('🚀 Starting VibeCheck API...');

  // Test database connection
  const dbConnected = await testConnection();
  if (!dbConnected) {
    console.error('❌ Failed to connect to database. Exiting...');
    process.exit(1);
  }

  // Initialize database schema
  const dbInitialized = await initializeDatabase();
  if (!dbInitialized) {
    console.error('❌ Failed to initialize database schema. Exiting...');
    process.exit(1);
  }

  // Create Elysia app
  const app = new Elysia()
    .use(swagger({
      documentation: {
        info: {
          title: 'VibeCheck API',
          version: '1.0.0',
          description: 'VibeCheck Backend API built with Bun and ElysiaJS'
        },
        servers: [
          {
            url: `http://localhost:${PORT}`,
            description: 'Development server'
          }
        ],
        tags: [
          {
            name: 'Health',
            description: 'Health check endpoints'
          },
          {
            name: 'Vibes',
            description: 'Vibe management endpoints'
          }
        ]
      }
    }))
    .use(cors({
      origin: FRONTEND_URL,
      methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
      allowedHeaders: ['Content-Type', 'Authorization'],
      credentials: true
    }))
    .use(routes)
    .onError(({ error, code, set }) => {
      console.error(`❌ Error ${code}:`, error.message);

      set.status = 500;

      if (code === 'VALIDATION') {
        set.status = 400;
        return {
          success: false,
          error: 'Validation failed',
          details: error.message
        };
      }

      if (code === 'NOT_FOUND') {
        set.status = 404;
        return {
          success: false,
          error: 'Endpoint not found'
        };
      }

      return {
        success: false,
        error: error.message || 'Internal server error'
      };
    })
    .onRequest(({ request }) => {
      console.log(`📝 ${request.method} ${new URL(request.url).pathname}`);
    })
    .listen(PORT);

  console.log(`🦊 VibeCheck API running at http://localhost:${PORT}`);
  console.log(`📊 Health check: http://localhost:${PORT}/api/health`);
  console.log(`🔗 API endpoints: http://localhost:${PORT}/api`);
  console.log(`📚 Swagger documentation: http://localhost:${PORT}/swagger`);
  console.log(`🌐 CORS enabled for: ${FRONTEND_URL}`);

  return app;
}

// Start the server
startServer().catch((error) => {
  console.error('❌ Failed to start server:', error);
  process.exit(1);
});

export default startServer;
