# VibeCheck Full-Stack Application

> A comprehensive development guide for building a modern CRUD application designed for AI-assisted coding

## 📋 Table of Contents

- [Project Overview](#project-overview)
- [Tech Stack](#tech-stack)
- [Prerequisites](#prerequisites)
- [Backend Development](#backend-development)
- [Frontend Development](#frontend-development)
- [Testing Strategy](#testing-strategy)
- [Deployment](#deployment)
- [Development Workflow](#development-workflow)

## 🎯 Project Overview

**VibeCheck** is a full-stack application that allows users to log, view, and manage their daily "vibes". This project serves as a comprehensive testing ground for AI-assisted development, featuring modern web technologies and best practices.

### Core Features

- ✅ **Create** new vibe entries with mood, rating, and date
- 📊 **Read** all vibes in a sortable, filterable table
- ✏️ **Update** existing vibe entries through an intuitive interface
- 🗑️ **Delete** unwanted vibe entries
- 🎨 **Theme management** with global state
- 📱 **Responsive design** with modern UI components

## 🛠 Tech Stack

| Layer | Technology | Purpose |
|-------|------------|---------|
| **Frontend** | Next.js (Pages Router) | React framework with SSR/SSG |
| **Styling** | Tailwind CSS | Utility-first CSS framework |
| **UI Components** | Shadcn/UI | Modern, accessible component library |
| **State Management** | Redux Toolkit | Predictable state container |
| **Backend** | Bun + ElysiaJS | Fast runtime with modern web framework |
| **Database** | PostgreSQL | Robust relational database |
| **Data Tables** | TanStack Table | Powerful table component |

## 📋 Prerequisites

Before starting development, ensure you have:

- [Bun](https://bun.sh/) runtime installed
- [PostgreSQL](https://www.postgresql.org/) database running
- [Node.js](https://nodejs.org/) (for frontend development)
- Basic knowledge of React, TypeScript, and SQL

---

## 🔧 Backend Development

The backend handles all business logic, API endpoints, and database interactions using Bun runtime with ElysiaJS framework.

### 🚀 Initial Setup

> **Prerequisites**: Ensure Bun runtime and PostgreSQL are installed and running.

```bash
# Navigate to your backend project directory
cd L:\Startup\Testing\MCP testing\ai-coding-testing\be

# Install core dependencies
bun add elysia @elysiajs/cors
bun add postgres dotenv

# Install development dependencies
bun add -d @types/bun
```

### 📁 Backend Project Structure

```
backend-project/
├── src/
│   ├── db/
│   │   ├── index.js           # Database connection & configuration
│   │   ├── schema.sql         # Database schema definitions
│   │   └── migrations/        # Database migration files
│   ├── controllers/
│   │   └── vibe.controller.js # Business logic for vibe operations
│   ├── routes/
│   │   ├── index.js          # Main route aggregator
│   │   └── vibes.js          # Vibe-specific routes
│   ├── middleware/
│   │   ├── cors.js           # CORS configuration
│   │   └── validation.js     # Request validation middleware
│   ├── types/
│   │   └── vibe.types.js     # Type definitions for vibes
│   └── index.js              # Application entry point
├── .env                      # Environment variables
├── .env.example             # Environment template
├── package.json
├── bun.lockb
└── README.md
```

### 🗄️ Database Configuration

#### 1. Environment Setup

Create `.env` file with database configuration:

```env
# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/vibecheck
PORT=3001
NODE_ENV=development

# CORS Configuration
FRONTEND_URL=http://localhost:3000
```

#### 2. Database Schema (`src/db/schema.sql`)

```sql
-- Create vibes table with proper constraints and indexes
CREATE TABLE IF NOT EXISTS vibes (
    id SERIAL PRIMARY KEY,
    user_name TEXT NOT NULL CHECK (length(user_name) > 0),
    mood VARCHAR(50) NOT NULL CHECK (mood IN ('Happy', 'Okay', 'Sad', 'Excited', 'Anxious')),
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    vibe_date DATE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_vibes_date ON vibes(vibe_date);
CREATE INDEX IF NOT EXISTS idx_vibes_mood ON vibes(mood);
CREATE INDEX IF NOT EXISTS idx_vibes_created_at ON vibes(created_at);

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_vibes_updated_at
    BEFORE UPDATE ON vibes
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
```

#### 3. Database Connection (`src/db/index.js`)

```javascript
import postgres from 'postgres';
import 'dotenv/config';

const connectionString = process.env.DATABASE_URL;

if (!connectionString) {
  throw new Error("DATABASE_URL environment variable is required");
}

// Configure postgres connection with proper settings
const sql = postgres(connectionString, {
  host: 'localhost',
  port: 5432,
  database: 'vibecheck',
  max: 20,
  idle_timeout: 20,
  connect_timeout: 10,
});

// Test database connection
try {
  await sql`SELECT 1`;
  console.log('✅ Database connected successfully');
} catch (error) {
  console.error('❌ Database connection failed:', error);
  process.exit(1);
}

export default sql;
```

### 🔌 API Implementation

#### Type Definitions (`src/types/vibe.types.js`)

```javascript
// Vibe data structure and validation schemas
export const VibeSchema = {
  id: 'number',
  user_name: 'string',
  mood: 'string',
  rating: 'number',
  vibe_date: 'string',
  created_at: 'string',
  updated_at: 'string'
};

export const CreateVibeSchema = {
  user_name: 'string',
  mood: 'string',
  rating: 'number',
  vibe_date: 'string'
};

export const VALID_MOODS = ['Happy', 'Okay', 'Sad', 'Excited', 'Anxious'];
```

#### Controller (`src/controllers/vibe.controller.js`)

```javascript
import sql from '../db/index.js';
import { VALID_MOODS } from '../types/vibe.types.js';

// Validation helper
const validateVibeData = (data) => {
  const { user_name, mood, rating, vibe_date } = data;

  if (!user_name?.trim()) {
    throw new Error('User name is required');
  }

  if (!VALID_MOODS.includes(mood)) {
    throw new Error(`Invalid mood. Must be one of: ${VALID_MOODS.join(', ')}`);
  }

  if (!rating || rating < 1 || rating > 5) {
    throw new Error('Rating must be between 1 and 5');
  }

  if (!vibe_date || isNaN(Date.parse(vibe_date))) {
    throw new Error('Valid date is required');
  }
};

export const getAllVibes = async () => {
  try {
    const vibes = await sql`
      SELECT
        id,
        user_name,
        mood,
        rating,
        vibe_date,
        created_at,
        updated_at
      FROM vibes
      ORDER BY created_at DESC
    `;
    return { success: true, data: vibes };
  } catch (error) {
    console.error('Error fetching vibes:', error);
    throw new Error('Failed to fetch vibes');
  }
};

export const createVibe = async ({ body }) => {
  try {
    validateVibeData(body);

    const { user_name, mood, rating, vibe_date } = body;
    const [newVibe] = await sql`
      INSERT INTO vibes (user_name, mood, rating, vibe_date)
      VALUES (${user_name.trim()}, ${mood}, ${rating}, ${vibe_date})
      RETURNING *
    `;

    return { success: true, data: newVibe };
  } catch (error) {
    console.error('Error creating vibe:', error);
    throw new Error(error.message || 'Failed to create vibe');
  }
};

export const updateVibe = async ({ params, body }) => {
  try {
    const { id } = params;
    validateVibeData(body);

    const { user_name, mood, rating, vibe_date } = body;
    const [updatedVibe] = await sql`
      UPDATE vibes
      SET user_name = ${user_name.trim()},
          mood = ${mood},
          rating = ${rating},
          vibe_date = ${vibe_date}
      WHERE id = ${id}
      RETURNING *
    `;

    if (!updatedVibe) {
      throw new Error('Vibe not found');
    }

    return { success: true, data: updatedVibe };
  } catch (error) {
    console.error('Error updating vibe:', error);
    throw new Error(error.message || 'Failed to update vibe');
  }
};

export const deleteVibe = async ({ params }) => {
  try {
    const { id } = params;
    const result = await sql`DELETE FROM vibes WHERE id = ${id}`;

    if (result.count === 0) {
      throw new Error('Vibe not found');
    }

    return { success: true, message: `Vibe ${id} deleted successfully` };
  } catch (error) {
    console.error('Error deleting vibe:', error);
    throw new Error(error.message || 'Failed to delete vibe');
  }
};
```

#### Routes (`src/routes/vibes.js`)

```javascript
import { Elysia, t } from 'elysia';
import { getAllVibes, createVibe, updateVibe, deleteVibe } from '../controllers/vibe.controller.js';

export const vibesRoutes = new Elysia({ prefix: '/vibes' })
  .get('/', getAllVibes)
  .post('/', createVibe, {
    body: t.Object({
      user_name: t.String({ minLength: 1 }),
      mood: t.String(),
      rating: t.Number({ minimum: 1, maximum: 5 }),
      vibe_date: t.String()
    })
  })
  .put('/:id', updateVibe, {
    params: t.Object({
      id: t.Number()
    }),
    body: t.Object({
      user_name: t.String({ minLength: 1 }),
      mood: t.String(),
      rating: t.Number({ minimum: 1, maximum: 5 }),
      vibe_date: t.String()
    })
  })
  .delete('/:id', deleteVibe, {
    params: t.Object({
      id: t.Number()
    })
  });
```

#### Main Routes (`src/routes/index.js`)

```javascript
import { Elysia } from 'elysia';
import { vibesRoutes } from './vibes.js';

export const routes = new Elysia({ prefix: '/api' })
  .use(vibesRoutes)
  .get('/health', () => ({
    status: 'ok',
    timestamp: new Date().toISOString(),
    service: 'VibeCheck API'
  }));
```

#### Server Entry Point (`src/index.js`)

```javascript
import { Elysia } from 'elysia';
import { cors } from '@elysiajs/cors';
import { routes } from './routes/index.js';
import 'dotenv/config';

const PORT = process.env.PORT || 3001;
const FRONTEND_URL = process.env.FRONTEND_URL || 'http://localhost:3000';

const app = new Elysia()
  .use(cors({
    origin: FRONTEND_URL,
    methods: ['GET', 'POST', 'PUT', 'DELETE'],
    allowedHeaders: ['Content-Type', 'Authorization'],
    credentials: true
  }))
  .use(routes)
  .onError(({ error, code }) => {
    console.error(`Error ${code}:`, error);

    if (code === 'VALIDATION') {
      return {
        success: false,
        error: 'Validation failed',
        details: error.message
      };
    }

    return {
      success: false,
      error: error.message || 'Internal server error'
    };
  })
  .listen(PORT);

console.log(`🦊 VibeCheck API running at http://localhost:${PORT}`);
console.log(`📊 Health check: http://localhost:${PORT}/api/health`);

export default app;
```

---

## 🎨 Frontend Development

The frontend provides an intuitive user interface for managing vibes with modern React patterns and components.

### 🚀 Frontend Setup

```bash
# Navigate to frontend directory
cd L:\Startup\Testing\MCP testing\ai-coding-testing\ai-coding-testing-fe

# Install core dependencies
npm install next react react-dom
npm install @reduxjs/toolkit react-redux
npm install tailwindcss @tailwindcss/forms
npm install @tanstack/react-table

# Install Shadcn/UI components
npx shadcn-ui@latest init
npx shadcn-ui@latest add button input select label
npx shadcn-ui@latest add table dialog popover calendar
npx shadcn-ui@latest add dropdown-menu radio-group
```

### 📁 Frontend Project Structure

```text
frontend-project/
└── src/
    ├── components/
    │   ├── ui/                     # Shadcn/UI components
    │   ├── forms/
    │   │   ├── VibeEntryForm.jsx   # Main data collection form
    │   │   └── EditVibeDialog.jsx  # Edit vibe modal
    │   ├── layout/
    │   │   ├── Navbar.jsx          # Navigation component
    │   │   └── Layout.jsx          # Main layout wrapper
    │   ├── tables/
    │   │   ├── VibesTable.jsx      # Main data table
    │   │   ├── columns.jsx         # Table column definitions
    │   │   └── TableActions.jsx    # Row action buttons
    │   └── theme/
    │       └── ThemeProvider.jsx   # Theme context provider
    ├── pages/
    │   ├── _app.js                 # App wrapper with providers
    │   ├── _document.js            # HTML document structure
    │   ├── index.js                # Home: Data collection
    │   ├── vibes.js                # Vibes: Data display & management
    │   └── state-test.js           # State management testing
    ├── store/
    │   ├── slices/
    │   │   ├── vibesSlice.js       # Vibes data management
    │   │   └── uiSlice.js          # UI state management
    │   └── index.js                # Redux store configuration
    ├── lib/
    │   ├── utils.js                # Utility functions
    │   ├── api.js                  # API client functions
    │   └── constants.js            # App constants
    └── styles/
        └── globals.css             # Global styles
```

### 🔧 Core Configuration

#### Redux Store Setup (`src/store/index.js`)

```javascript
import { configureStore } from '@reduxjs/toolkit';
import vibesReducer from './slices/vibesSlice';
import uiReducer from './slices/uiSlice';

export const store = configureStore({
  reducer: {
    vibes: vibesReducer,
    ui: uiReducer,
  },
  middleware: (getDefaultMiddleware) =>
    getDefaultMiddleware({
      serializableCheck: {
        ignoredActions: ['persist/PERSIST'],
      },
    }),
});

export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
```

#### API Client (`src/lib/api.js`)

```javascript
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:3001/api';

class ApiClient {
  async request(endpoint, options = {}) {
    const url = `${API_BASE_URL}${endpoint}`;
    const config = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || `HTTP error! status: ${response.status}`);
      }

      return data;
    } catch (error) {
      console.error('API request failed:', error);
      throw error;
    }
  }

  // Vibes API methods
  async getVibes() {
    return this.request('/vibes');
  }

  async createVibe(vibeData) {
    return this.request('/vibes', {
      method: 'POST',
      body: JSON.stringify(vibeData),
    });
  }

  async updateVibe(id, vibeData) {
    return this.request(`/vibes/${id}`, {
      method: 'PUT',
      body: JSON.stringify(vibeData),
    });
  }

  async deleteVibe(id) {
    return this.request(`/vibes/${id}`, {
      method: 'DELETE',
    });
  }
}

export const apiClient = new ApiClient();
```

### 📄 Page Implementation

#### Page 1: Data Collection (`pages/index.js`)

**Purpose**: Collect user vibe data and submit to backend

**Features**:
- Form validation with real-time feedback
- Responsive design with Shadcn/UI components
- Success/error state management
- Form reset after successful submission

**Components Used**:
- `Input` for user name
- `Select` dropdown for mood selection
- `RadioGroup` for rating (1-5 stars)
- `DatePicker` with `Popover` for date selection
- `Button` for form submission
- `Alert` for feedback messages

**Implementation Strategy**:
- Use `useState` for form state management
- Implement form validation with custom hooks
- Use Redux for global state updates
- Handle loading states and error feedback

#### Page 2: Data Display & Management (`pages/vibes.js`)

**Purpose**: Display all vibes in an interactive table with full CRUD operations

**Features**:
- Sortable and filterable data table
- Inline editing with modal dialogs
- Bulk operations support
- Real-time data updates
- Responsive table design

**Components Used**:
- `Table` with TanStack Table integration
- `DropdownMenu` for row actions
- `Dialog` for edit/delete confirmations
- `Input` for search and filters
- `Badge` for mood indicators
- `Pagination` for large datasets

**Implementation Strategy**:
- Use `useEffect` to fetch data on page load
- Implement optimistic updates for better UX
- Use Redux for state management
- Handle loading and error states gracefully

#### Page 3: State Management Testing (`pages/state-test.js`)

**Purpose**: Demonstrate local and global state management patterns

**Features**:
- Local state examples with hooks
- Global state with Redux Toolkit
- Component communication patterns
- State persistence examples

### 🧪 State Management Examples

#### Local State Management

```javascript
// Local counter component
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export function LocalStateExample() {
  const [count, setCount] = useState(0);
  const [name, setName] = useState('');

  return (
    <Card>
      <CardHeader>
        <CardTitle>Local State Example</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div>
          <p className="text-lg">Count: {count}</p>
          <div className="space-x-2">
            <Button onClick={() => setCount(c => c + 1)}>
              Increment
            </Button>
            <Button onClick={() => setCount(c => c - 1)}>
              Decrement
            </Button>
            <Button onClick={() => setCount(0)} variant="outline">
              Reset
            </Button>
          </div>
        </div>

        <div>
          <input
            type="text"
            value={name}
            onChange={(e) => setName(e.target.value)}
            placeholder="Enter your name"
            className="border rounded px-3 py-2"
          />
          {name && <p>Hello, {name}!</p>}
        </div>
      </CardContent>
    </Card>
  );
}
```

#### Global State Management

```javascript
// Redux slice for theme management
import { createSlice } from '@reduxjs/toolkit';

const uiSlice = createSlice({
  name: 'ui',
  initialState: {
    theme: 'light',
    sidebarOpen: false,
    notifications: [],
  },
  reducers: {
    toggleTheme: (state) => {
      state.theme = state.theme === 'light' ? 'dark' : 'light';
    },
    toggleSidebar: (state) => {
      state.sidebarOpen = !state.sidebarOpen;
    },
    addNotification: (state, action) => {
      state.notifications.push({
        id: Date.now(),
        message: action.payload,
        timestamp: new Date().toISOString(),
      });
    },
    removeNotification: (state, action) => {
      state.notifications = state.notifications.filter(
        n => n.id !== action.payload
      );
    },
  },
});

export const {
  toggleTheme,
  toggleSidebar,
  addNotification,
  removeNotification
} = uiSlice.actions;
export default uiSlice.reducer;
```

```javascript
// Theme selector component
import { useSelector, useDispatch } from 'react-redux';
import { toggleTheme } from '@/store/slices/uiSlice';
import { Button } from '@/components/ui/button';

export function ThemeSelector() {
  const theme = useSelector(state => state.ui.theme);
  const dispatch = useDispatch();

  return (
    <Button
      onClick={() => dispatch(toggleTheme())}
      variant="outline"
    >
      Switch to {theme === 'light' ? 'Dark' : 'Light'} Mode
    </Button>
  );
}

// Profile header component
export function ProfileHeader() {
  const theme = useSelector(state => state.ui.theme);

  return (
    <div className={`p-4 rounded-lg ${
      theme === 'dark' ? 'bg-gray-800 text-white' : 'bg-gray-100 text-black'
    }`}>
      <h2>Current Theme: {theme}</h2>
      <p>This component updates automatically when theme changes!</p>
    </div>
  );
}
```

## 🧪 Testing Strategy

### Backend Testing

#### Unit Tests (`tests/controllers/vibe.test.js`)

```javascript
import { describe, it, expect, beforeEach, afterEach } from 'bun:test';
import { getAllVibes, createVibe, updateVibe, deleteVibe } from '../src/controllers/vibe.controller.js';

describe('Vibe Controller', () => {
  beforeEach(async () => {
    // Setup test database
    await sql`DELETE FROM vibes WHERE user_name LIKE 'test_%'`;
  });

  it('should create a new vibe', async () => {
    const vibeData = {
      user_name: 'test_user',
      mood: 'Happy',
      rating: 5,
      vibe_date: '2024-01-01'
    };

    const result = await createVibe({ body: vibeData });
    expect(result.success).toBe(true);
    expect(result.data.user_name).toBe('test_user');
  });

  it('should validate required fields', async () => {
    const invalidData = { mood: 'Happy' };

    await expect(createVibe({ body: invalidData }))
      .rejects.toThrow('User name is required');
  });
});
```

#### API Integration Tests

```bash
# Install testing dependencies
bun add -d @types/bun

# Run tests
bun test
```

### Frontend Testing

#### Component Tests (`__tests__/components/VibeEntryForm.test.jsx`)

```javascript
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { store } from '@/store';
import VibeEntryForm from '@/components/forms/VibeEntryForm';

const renderWithProvider = (component) => {
  return render(
    <Provider store={store}>
      {component}
    </Provider>
  );
};

describe('VibeEntryForm', () => {
  it('renders form fields correctly', () => {
    renderWithProvider(<VibeEntryForm />);

    expect(screen.getByLabelText(/user name/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/mood/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/rating/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/date/i)).toBeInTheDocument();
  });

  it('submits form with valid data', async () => {
    const mockSubmit = jest.fn();
    renderWithProvider(<VibeEntryForm onSubmit={mockSubmit} />);

    fireEvent.change(screen.getByLabelText(/user name/i), {
      target: { value: 'John Doe' }
    });

    fireEvent.click(screen.getByRole('button', { name: /submit/i }));

    await waitFor(() => {
      expect(mockSubmit).toHaveBeenCalledWith(
        expect.objectContaining({
          user_name: 'John Doe'
        })
      );
    });
  });
});
```

#### Setup Testing Environment

```bash
# Install testing dependencies
npm install -D jest @testing-library/react @testing-library/jest-dom
npm install -D jest-environment-jsdom

# Add to package.json
{
  "scripts": {
    "test": "jest",
    "test:watch": "jest --watch",
    "test:coverage": "jest --coverage"
  }
}
```

---

## 🚀 Deployment

### Backend Deployment

#### Docker Configuration (`Dockerfile`)

```dockerfile
FROM oven/bun:1 as base
WORKDIR /app

# Install dependencies
COPY package.json bun.lockb ./
RUN bun install --frozen-lockfile

# Copy source code
COPY src ./src
COPY .env.production .env

# Expose port
EXPOSE 3001

# Start the application
CMD ["bun", "run", "src/index.js"]
```

#### Docker Compose (`docker-compose.yml`)

```yaml
version: '3.8'

services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: vibecheck
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  backend:
    build: .
    ports:
      - "3001:3001"
    environment:
      DATABASE_URL: ********************************************/vibecheck
      NODE_ENV: production
    depends_on:
      - postgres

volumes:
  postgres_data:
```

### Frontend Deployment

#### Vercel Deployment

```bash
# Install Vercel CLI
npm i -g vercel

# Deploy to Vercel
vercel --prod

# Environment variables in Vercel dashboard:
# NEXT_PUBLIC_API_URL=https://your-backend-url.com/api
```

#### Build Configuration (`next.config.js`)

```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  swcMinify: true,
  env: {
    NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL,
  },
  async rewrites() {
    return [
      {
        source: '/api/:path*',
        destination: `${process.env.NEXT_PUBLIC_API_URL}/:path*`,
      },
    ];
  },
};

module.exports = nextConfig;
```

---

## 🔄 Development Workflow

### Getting Started

1. **Clone and Setup**
   ```bash
   git clone <repository-url>
   cd vibecheck

   # Setup backend
   cd backend
   bun install
   cp .env.example .env
   # Configure DATABASE_URL in .env

   # Setup frontend
   cd ../frontend
   npm install
   cp .env.local.example .env.local
   # Configure NEXT_PUBLIC_API_URL in .env.local
   ```

2. **Database Setup**
   ```bash
   # Start PostgreSQL
   # Run schema.sql to create tables
   psql -d vibecheck -f src/db/schema.sql
   ```

3. **Start Development Servers**
   ```bash
   # Terminal 1: Backend
   cd backend && bun run dev

   # Terminal 2: Frontend
   cd frontend && npm run dev
   ```

### Development Best Practices

- **Code Organization**: Follow the established folder structure
- **State Management**: Use Redux for global state, local state for component-specific data
- **API Design**: Follow RESTful conventions with proper error handling
- **Testing**: Write tests for critical functionality
- **Git Workflow**: Use feature branches and meaningful commit messages
- **Documentation**: Keep README files updated with setup instructions

### Debugging Tips

- Use browser dev tools for frontend debugging
- Use `console.log` strategically in backend for API debugging
- Check network tab for API request/response issues
- Use Redux DevTools for state debugging
- Monitor database queries for performance issues

---

## 📚 Additional Resources

### Documentation Links

- [Next.js Documentation](https://nextjs.org/docs)
- [ElysiaJS Documentation](https://elysiajs.com)
- [Redux Toolkit Documentation](https://redux-toolkit.js.org)
- [Shadcn/UI Components](https://ui.shadcn.com)
- [TanStack Table](https://tanstack.com/table)
- [Tailwind CSS](https://tailwindcss.com)

### Useful Commands

```bash
# Backend commands
bun run dev          # Start development server
bun test            # Run tests
bun run build       # Build for production

# Frontend commands
npm run dev         # Start development server
npm run build       # Build for production
npm run test        # Run tests
npm run lint        # Run ESLint
```

---

## 🎯 Success Criteria

By completing this project, you will have:

- ✅ A fully functional CRUD application
- ✅ Modern backend with Bun and ElysiaJS
- ✅ React frontend with Next.js and Shadcn/UI
- ✅ Global state management with Redux Toolkit
- ✅ Responsive design with Tailwind CSS
- ✅ Interactive data tables with TanStack Table
- ✅ Comprehensive testing setup
- ✅ Production-ready deployment configuration
- ✅ Clear development workflow and documentation

This project serves as an excellent foundation for AI-assisted development and can be extended with additional features like authentication, real-time updates, and advanced analytics.