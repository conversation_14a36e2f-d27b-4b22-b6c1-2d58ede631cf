import { describe, it, expect, beforeEach } from 'bun:test';
import { getAllVibes, createVibe } from './vibe.controller.js';

describe('Vibe Controller', () => {
  it('should validate required fields for createVibe', async () => {
    const invalidData = { 
      body: { 
        mood: 'Happy',
        rating: 5,
        vibe_date: '2024-01-01'
        // missing user_name
      } 
    };
    
    try {
      await createVibe(invalidData as any);
      expect(false).toBe(true); // Should not reach here
    } catch (error: any) {
      expect(error.message).toContain('User name is required');
    }
  });

  it('should validate mood values', async () => {
    const invalidData = { 
      body: { 
        user_name: 'Test User',
        mood: 'InvalidMood',
        rating: 5,
        vibe_date: '2024-01-01'
      } 
    };
    
    try {
      await createVibe(invalidData as any);
      expect(false).toBe(true); // Should not reach here
    } catch (error: any) {
      expect(error.message).toContain('Invalid mood');
    }
  });

  it('should validate rating range', async () => {
    const invalidData = { 
      body: { 
        user_name: 'Test User',
        mood: 'Happy',
        rating: 10, // Invalid rating
        vibe_date: '2024-01-01'
      } 
    };
    
    try {
      await createVibe(invalidData as any);
      expect(false).toBe(true); // Should not reach here
    } catch (error: any) {
      expect(error.message).toContain('Rating must be between 1 and 5');
    }
  });
});
