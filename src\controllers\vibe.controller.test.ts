import { describe, it, expect } from 'bun:test';
import { createVibe, updateVibe, deleteVibe } from './vibe.controller.js';

describe('Vibe Controller Validation', () => {
  it('should validate required user_name field', async () => {
    const invalidData = {
      body: {
        user_name: '',
        mood: 'Happy',
        rating: 5,
        vibe_date: '2024-01-01'
      }
    };

    try {
      await createVibe(invalidData as any);
      expect(false).toBe(true); // Should not reach here
    } catch (error: any) {
      expect(error.message).toContain('User name is required');
    }
  });

  it('should validate mood values', async () => {
    const invalidData = {
      body: {
        user_name: 'Test User',
        mood: 'InvalidMood',
        rating: 5,
        vibe_date: '2024-01-01'
      }
    };

    try {
      await createVibe(invalidData as any);
      expect(false).toBe(true); // Should not reach here
    } catch (error: any) {
      expect(error.message).toContain('Invalid mood');
    }
  });

  it('should validate rating range', async () => {
    const invalidData = {
      body: {
        user_name: 'Test User',
        mood: 'Happy',
        rating: 10, // Invalid rating
        vibe_date: '2024-01-01'
      }
    };

    try {
      await createVibe(invalidData as any);
      expect(false).toBe(true); // Should not reach here
    } catch (error: any) {
      expect(error.message).toContain('Rating must be an integer between 1 and 5');
    }
  });

  it('should validate date format', async () => {
    const invalidData = {
      body: {
        user_name: 'Test User',
        mood: 'Happy',
        rating: 5,
        vibe_date: 'invalid-date'
      }
    };

    try {
      await createVibe(invalidData as any);
      expect(false).toBe(true); // Should not reach here
    } catch (error: any) {
      expect(error.message).toContain('Valid date is required');
    }
  });

  it('should validate ID for update operations', async () => {
    const invalidParams = {
      params: { id: 'invalid-id' },
      body: {
        user_name: 'Test User',
        mood: 'Happy',
        rating: 5,
        vibe_date: '2024-01-01'
      }
    };

    try {
      await updateVibe(invalidParams as any);
      expect(false).toBe(true); // Should not reach here
    } catch (error: any) {
      expect(error.message).toContain('Invalid vibe ID');
    }
  });

  it('should validate ID for delete operations', async () => {
    const invalidParams = {
      params: { id: 'abc' }
    };

    try {
      await deleteVibe(invalidParams as any);
      expect(false).toBe(true); // Should not reach here
    } catch (error: any) {
      expect(error.message).toContain('Invalid vibe ID');
    }
  });
});
