// Vibe data structure and validation schemas
export interface Vibe {
  id: number;
  user_name: string;
  mood: string;
  rating: number;
  vibe_date: string;
  created_at: string;
  updated_at: string;
}

export interface CreateVibeRequest {
  user_name: string;
  mood: string;
  rating: number;
  vibe_date: string;
}

export interface UpdateVibeRequest {
  user_name: string;
  mood: string;
  rating: number;
  vibe_date: string;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export const VALID_MOODS = ['Happy', 'Okay', 'Sad', 'Excited', 'Anxious'] as const;
export type ValidMood = typeof VALID_MOODS[number];

// Validation schemas for Elysia
export const VibeSchema = {
  id: 'number',
  user_name: 'string',
  mood: 'string',
  rating: 'number',
  vibe_date: 'string',
  created_at: 'string',
  updated_at: 'string'
} as const;

export const CreateVibeSchema = {
  user_name: 'string',
  mood: 'string', 
  rating: 'number',
  vibe_date: 'string'
} as const;
