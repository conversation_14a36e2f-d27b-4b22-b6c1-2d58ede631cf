-- Create vibes table with proper constraints and indexes
CREATE TABLE IF NOT EXISTS vibes (
    id SERIAL PRIMARY KEY,
    user_name TEXT NOT NULL CHECK (length(user_name) > 0),
    mood VARCHAR(50) NOT NULL CHECK (mood IN ('Happy', 'Okay', 'Sad', 'Excited', 'Anxious')),
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    vibe_date DATE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better query performance
CREATE INDEX IF NOT EXISTS idx_vibes_date ON vibes(vibe_date);
CREATE INDEX IF NOT EXISTS idx_vibes_mood ON vibes(mood);
CREATE INDEX IF NOT EXISTS idx_vibes_created_at ON vibes(created_at);

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_vibes_updated_at 
    BEFORE UPDATE ON vibes 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Insert some sample data for testing
INSERT INTO vibes (user_name, mood, rating, vibe_date) VALUES
    ('Alice Johnson', 'Happy', 5, '2024-01-15'),
    ('Bob Smith', 'Okay', 3, '2024-01-15'),
    ('Charlie Brown', 'Excited', 4, '2024-01-14'),
    ('Diana Prince', 'Sad', 2, '2024-01-14'),
    ('Eve Wilson', 'Anxious', 2, '2024-01-13')
ON CONFLICT DO NOTHING;
