import { Elysia } from 'elysia';
import { vibesRoutes } from './vibes.js';

export const routes = new Elysia({ prefix: '/api' })
  .use(vibesRoutes)
  .get('/health', () => ({
    status: 'ok',
    timestamp: new Date().toISOString(),
    service: 'VibeCheck API',
    version: '1.0.0'
  }), {
    detail: {
      summary: 'Health check',
      description: 'Check if the API is running and healthy',
      tags: ['Health']
    }
  })
  .get('/', () => ({
    message: 'Welcome to VibeCheck API',
    version: '1.0.0',
    endpoints: {
      health: '/api/health',
      vibes: '/api/vibes'
    }
  }), {
    detail: {
      summary: 'API information',
      description: 'Get basic information about the API and available endpoints',
      tags: ['Health']
    }
  });
