import { Elysia } from 'elysia';
import { vibesRoutes } from './vibes.js';

export const routes = new Elysia({ prefix: '/api' })
  .use(vibesRoutes)
  .get('/health', () => ({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    service: 'VibeCheck API',
    version: '1.0.0'
  }))
  .get('/', () => ({
    message: 'Welcome to VibeCheck API',
    version: '1.0.0',
    endpoints: {
      health: '/api/health',
      vibes: '/api/vibes'
    }
  }));
