import sql from '../db/index.js';
import { VALID_MOODS, type CreateVibeRequest, type UpdateVibeRequest, type ApiResponse, type Vibe } from '../types/vibe.types.js';

// Validation helper
const validateVibeData = (data: CreateVibeRequest | UpdateVibeRequest): void => {
  const errors: string[] = [];

  if (!data.user_name?.trim()) {
    errors.push('User name is required and cannot be empty');
  }

  if (!data.mood || !VALID_MOODS.includes(data.mood as any)) {
    errors.push(`Invalid mood. Must be one of: ${VALID_MOODS.join(', ')}`);
  }

  if (!data.rating || !Number.isInteger(data.rating) || data.rating < 1 || data.rating > 5) {
    errors.push('Rating must be an integer between 1 and 5');
  }

  if (!data.vibe_date || isNaN(Date.parse(data.vibe_date))) {
    errors.push('Valid date is required (YYYY-MM-DD format)');
  }

  if (errors.length > 0) {
    throw new Error(errors.join('; '));
  }
};

export const getAllVibes = async (): Promise<ApiResponse<Vibe[]>> => {
  try {
    const vibes = await sql`
      SELECT
        id,
        user_name,
        mood,
        rating,
        vibe_date::text as vibe_date,
        created_at::text as created_at,
        updated_at::text as updated_at
      FROM vibes
      ORDER BY created_at DESC
    `;

    console.log(`📊 Retrieved ${vibes.length} vibes from database`);
    return { success: true, data: vibes as Vibe[] };
  } catch (error: any) {
    console.error('❌ Error fetching vibes:', error);
    throw new Error(`Failed to fetch vibes: ${error.message}`);
  }
};

export const createVibe = async ({ body }: { body: CreateVibeRequest }): Promise<ApiResponse<Vibe>> => {
  try {
    validateVibeData(body);

    const { user_name, mood, rating, vibe_date } = body;
    const [newVibe] = await sql`
      INSERT INTO vibes (user_name, mood, rating, vibe_date)
      VALUES (${user_name.trim()}, ${mood}, ${rating}, ${vibe_date})
      RETURNING
        id,
        user_name,
        mood,
        rating,
        vibe_date::text as vibe_date,
        created_at::text as created_at,
        updated_at::text as updated_at
    `;

    console.log(`✅ Created new vibe for user: ${user_name.trim()}`);
    return { success: true, data: newVibe as Vibe };
  } catch (error: any) {
    console.error('❌ Error creating vibe:', error);
    throw new Error(error.message || 'Failed to create vibe');
  }
};

export const updateVibe = async ({ params, body }: { params: { id: string }, body: UpdateVibeRequest }): Promise<ApiResponse<Vibe>> => {
  try {
    const { id } = params;

    // Validate ID
    const vibeId = parseInt(id);
    if (isNaN(vibeId) || vibeId <= 0) {
      throw new Error('Invalid vibe ID');
    }

    validateVibeData(body);

    const { user_name, mood, rating, vibe_date } = body;
    const [updatedVibe] = await sql`
      UPDATE vibes
      SET user_name = ${user_name.trim()},
          mood = ${mood},
          rating = ${rating},
          vibe_date = ${vibe_date}
      WHERE id = ${vibeId}
      RETURNING
        id,
        user_name,
        mood,
        rating,
        vibe_date::text as vibe_date,
        created_at::text as created_at,
        updated_at::text as updated_at
    `;

    if (!updatedVibe) {
      throw new Error(`Vibe with ID ${vibeId} not found`);
    }

    console.log(`✅ Updated vibe ID: ${vibeId}`);
    return { success: true, data: updatedVibe as Vibe };
  } catch (error: any) {
    console.error('❌ Error updating vibe:', error);
    throw new Error(error.message || 'Failed to update vibe');
  }
};

export const deleteVibe = async ({ params }: { params: { id: string } }): Promise<ApiResponse> => {
  try {
    const { id } = params;

    // Validate ID
    const vibeId = parseInt(id);
    if (isNaN(vibeId) || vibeId <= 0) {
      throw new Error('Invalid vibe ID');
    }

    const result = await sql`DELETE FROM vibes WHERE id = ${vibeId}`;

    if (result.count === 0) {
      throw new Error(`Vibe with ID ${vibeId} not found`);
    }

    console.log(`🗑️ Deleted vibe ID: ${vibeId}`);
    return { success: true, message: `Vibe ${vibeId} deleted successfully` };
  } catch (error: any) {
    console.error('❌ Error deleting vibe:', error);
    throw new Error(error.message || 'Failed to delete vibe');
  }
};
