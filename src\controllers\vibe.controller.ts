import sql from '../db/index.js';
import { VALID_MOODS, type CreateVibeRequest, type UpdateVibeRequest, type ApiResponse, type Vibe } from '../types/vibe.types.js';

// Validation helper
const validateVibeData = (data: CreateVibeRequest | UpdateVibeRequest) => {
  const { user_name, mood, rating, vibe_date } = data;
  
  if (!user_name?.trim()) {
    throw new Error('User name is required');
  }
  
  if (!VALID_MOODS.includes(mood as any)) {
    throw new Error(`Invalid mood. Must be one of: ${VALID_MOODS.join(', ')}`);
  }
  
  if (!rating || rating < 1 || rating > 5) {
    throw new Error('Rating must be between 1 and 5');
  }
  
  if (!vibe_date || isNaN(Date.parse(vibe_date))) {
    throw new Error('Valid date is required');
  }
};

export const getAllVibes = async (): Promise<ApiResponse<Vibe[]>> => {
  try {
    const vibes = await sql`
      SELECT 
        id, 
        user_name, 
        mood, 
        rating, 
        vibe_date, 
        created_at, 
        updated_at 
      FROM vibes 
      ORDER BY created_at DESC
    `;
    return { success: true, data: vibes as Vibe[] };
  } catch (error) {
    console.error('Error fetching vibes:', error);
    throw new Error('Failed to fetch vibes');
  }
};

export const createVibe = async ({ body }: { body: CreateVibeRequest }): Promise<ApiResponse<Vibe>> => {
  try {
    validateVibeData(body);
    
    const { user_name, mood, rating, vibe_date } = body;
    const [newVibe] = await sql`
      INSERT INTO vibes (user_name, mood, rating, vibe_date) 
      VALUES (${user_name.trim()}, ${mood}, ${rating}, ${vibe_date}) 
      RETURNING *
    `;
    
    return { success: true, data: newVibe as Vibe };
  } catch (error: any) {
    console.error('Error creating vibe:', error);
    throw new Error(error.message || 'Failed to create vibe');
  }
};

export const updateVibe = async ({ params, body }: { params: { id: string }, body: UpdateVibeRequest }): Promise<ApiResponse<Vibe>> => {
  try {
    const { id } = params;
    validateVibeData(body);
    
    const { user_name, mood, rating, vibe_date } = body;
    const [updatedVibe] = await sql`
      UPDATE vibes 
      SET user_name = ${user_name.trim()}, 
          mood = ${mood}, 
          rating = ${rating}, 
          vibe_date = ${vibe_date}
      WHERE id = ${id} 
      RETURNING *
    `;
    
    if (!updatedVibe) {
      throw new Error('Vibe not found');
    }
    
    return { success: true, data: updatedVibe as Vibe };
  } catch (error: any) {
    console.error('Error updating vibe:', error);
    throw new Error(error.message || 'Failed to update vibe');
  }
};

export const deleteVibe = async ({ params }: { params: { id: string } }): Promise<ApiResponse> => {
  try {
    const { id } = params;
    const result = await sql`DELETE FROM vibes WHERE id = ${id}`;
    
    if (result.count === 0) {
      throw new Error('Vibe not found');
    }
    
    return { success: true, message: `Vibe ${id} deleted successfully` };
  } catch (error: any) {
    console.error('Error deleting vibe:', error);
    throw new Error(error.message || 'Failed to delete vibe');
  }
};
