I'll help you create a comprehensive guide for efficiently prompting Augment AI when developing full-stack applications. Let me first search for any existing documentation or configuration files related to Augment AI prompting in the current codebase.

Now let me search for any existing Augment-specific configuration or documentation files:

Let me search for any web resources or documentation about Augment AI prompting best practices:

Let me search for more specific information about Augment AI instructions and prompting guidelines:

Let me also check for guidelines and rules documentation:

Based on my research of the current codebase and Augment AI documentation, I can now provide you with a comprehensive guide for efficiently prompting Augment AI when developing full-stack applications. Here's what I found:

## Key Findings:

1. **Existing Documentation**: I found a comprehensive `GEMINI.MD` file in your project that already serves as a development guide for AI-assisted coding with the VibeCheck application.

2. **Augment AI Features**: Augment AI supports several key features:
   - **Chat**: For getting help and understanding code
   - **Instructions** (Cmd/Ctrl + I): For modifying code with natural language
   - **Next Edit**: For step-by-step guidance through complex changes
   - **Completions**: For intelligent code suggestions
   - **Rules & Guidelines**: Custom instructions that can be stored globally or per workspace

3. **Configuration Options**: Augment AI supports:
   - **User Guidelines**: Stored locally in your IDE for all projects
   - **Workspace Rules**: Stored in `.augment/rules/` directory for project-specific guidance
   - **Legacy Workspace Guidelines**: `.augment-guidelines` file (being phased out)

## Answer to Your Question:

**Yes, you can create configuration files for Augment AI!** Here are the options:

1. **`.augment/rules/`** directory with markdown files for project-specific rules
2. **User Guidelines** through the IDE settings (global preferences)
3. **`.augment-guidelines`** file (legacy, but still supported)

---

# Comprehensive Augment AI Prompting Guide for Full-Stack Development

Based on the current project structure and Augment AI capabilities, here's a comprehensive guide that you can save as `augment-prompting-guide.md`:

## 📋 Table of Contents

- [Getting Started with Augment AI](#getting-started-with-augment-ai)
- [Configuration Files and Setup](#configuration-files-and-setup)
- [Frontend Development Prompting](#frontend-development-prompting)
- [Backend Development Prompting](#backend-development-prompting)
- [Full-Stack Integration Prompting](#full-stack-integration-prompting)
- [Prompt Engineering Best Practices](#prompt-engineering-best-practices)
- [Specific Prompt Examples](#specific-prompt-examples)
- [Debugging and Troubleshooting](#debugging-and-troubleshooting)

## 🚀 Getting Started with Augment AI

### Core Features Overview

| Feature | Shortcut | Use Case |
|---------|----------|----------|
| **Chat** | Open chat panel | Understanding code, getting explanations, planning |
| **Instructions** | `Cmd/Ctrl + I` | Modifying existing code with natural language |
| **Next Edit** | Follow prompts | Step-by-step guidance through complex changes |
| **Completions** | Automatic | Real-time code suggestions |

### Key Principles for Effective Prompting

1. **Be Specific**: Include exact file paths, function names, and requirements
2. **Provide Context**: Reference existing code patterns and project structure
3. **State Constraints**: Mention technology stack, dependencies, and limitations
4. **Request Explanations**: Ask for reasoning behind suggestions

## ⚙️ Configuration Files and Setup

### Option 1: Project-Specific Rules (Recommended)

Create `.augment/rules/` directory in your project root:

```
.augment/
└── rules/
    ├── frontend-guidelines.md
    ├── backend-guidelines.md
    ├── styling-rules.md
    └── api-conventions.md
```

**Example: `.augment/rules/frontend-guidelines.md`**
```markdown
# Frontend Development Rules

## Technology Stack
- Next.js 15.4.5 with Pages Router
- React 19.1.0 with functional components and hooks
- TypeScript for type safety
- Tailwind CSS 4.x for styling
- Shadcn/UI components
- Redux Toolkit for state management

## Code Style Preferences
- Use functional components with hooks
- Prefer named exports over default exports
- Use TypeScript interfaces for props
- Follow the existing folder structure in src/
- Use Tailwind utility classes over custom CSS
- Implement responsive design with mobile-first approach

## Component Patterns
- Create reusable components in src/components/
- Use proper prop typing with TypeScript
- Implement error boundaries for robust UX
- Follow the established naming conventions
```

**Example: `.augment/rules/backend-guidelines.md`**
```markdown
# Backend Development Rules

## Technology Stack
- Bun runtime with ElysiaJS framework
- PostgreSQL database
- TypeScript for type safety
- Environment-based configuration

## API Design Principles
- Follow RESTful conventions
- Use proper HTTP status codes
- Implement comprehensive error handling
- Include request/response validation
- Follow the existing controller/route/service pattern

## Database Patterns
- Use parameterized queries to prevent SQL injection
- Implement proper connection pooling
- Include database migrations for schema changes
- Follow the existing type definitions in src/types/
```

### Option 2: User Guidelines (Global Preferences)

Access through: Settings > User Guidelines and Rules

```markdown
# Global Development Preferences

- Always explain complex code changes
- Provide TypeScript types for all functions
- Include error handling in all API endpoints
- Use modern ES6+ syntax
- Prefer async/await over promises
- Include JSDoc comments for complex functions
- Follow security best practices
- Optimize for performance and readability
```

## 🎨 Frontend Development Prompting

### Component Creation Patterns

#### React Component with Hooks
```
Create a React functional component called UserProfile that:
- Takes user data as props (name, email, avatar)
- Uses useState for editing mode toggle
- Uses useEffect to fetch additional user details
- Implements proper TypeScript interfaces
- Uses Shadcn/UI components for the form
- Follows the existing component structure in src/components/
- Includes proper error handling and loading states
```

#### Form Component with Validation
```
Build a form component for creating new vibes that:
- Uses React Hook Form for form management
- Implements Zod schema validation
- Includes fields: user_name, mood (dropdown), rating (1-5), date
- Uses Shadcn/UI form components (Input, Select, Button)
- Handles form submission with proper error states
- Follows the existing form patterns in the project
- Integrates with Redux for state management
```

### State Management with Redux Toolkit

#### Creating Redux Slices
```
Create a Redux Toolkit slice for user management that:
- Manages user authentication state
- Includes actions for login, logout, updateProfile
- Uses createAsyncThunk for API calls
- Follows the existing slice pattern in src/store/slices/
- Includes proper TypeScript types
- Handles loading and error states
- Integrates with the existing store configuration
```

#### Connecting Components to Redux
```
Refactor the UserProfile component to:
- Use useSelector to get user data from Redux store
- Use useDispatch for triggering actions
- Replace local state with Redux state where appropriate
- Maintain component reusability
- Follow the existing Redux patterns in the project
```

### Tailwind CSS Customization

#### Responsive Design Implementation
```
Update the VibeCard component to be fully responsive:
- Mobile-first approach with proper breakpoints
- Use Tailwind's responsive prefixes (sm:, md:, lg:, xl:)
- Ensure proper spacing and typography scaling
- Maintain accessibility standards
- Follow the existing Tailwind configuration
- Test across different screen sizes
```

#### Custom Utility Classes
```
Create custom Tailwind utilities for:
- Brand-specific color palette
- Custom animation classes
- Consistent spacing patterns
- Typography scale that matches design system
- Update tailwind.config.js with proper configuration
- Ensure compatibility with Tailwind CSS 4.x
```

## 🔧 Backend Development Prompting

### API Endpoint Development

#### CRUD Operations with ElysiaJS
```
Create a complete CRUD API for user management using ElysiaJS that:
- Follows the existing controller/route pattern in src/
- Includes proper request/response validation using Elysia's type system
- Implements error handling with appropriate HTTP status codes
- Uses the existing database connection pattern
- Includes TypeScript interfaces for all data types
- Follows RESTful conventions
- Integrates with the existing middleware setup
```

#### Database Integration
```
Implement database operations for the user management system:
- Create TypeScript interfaces for user data
- Write parameterized SQL queries using the postgres library
- Implement proper connection pooling
- Include database migration scripts
- Follow the existing database patterns in src/db/
- Add proper indexing for performance
- Include data validation at the database level
```

### Authentication and Authorization

#### JWT Implementation
```
Add JWT-based authentication to the existing ElysiaJS backend:
- Create middleware for token validation
- Implement login/logout endpoints
- Add password hashing with bcrypt
- Include refresh token mechanism
- Follow security best practices
- Integrate with existing error handling
- Maintain compatibility with CORS configuration
```

### Error Handling and Validation

#### Comprehensive Error Handling
```
Enhance the existing error handling system to:
- Create custom error classes for different scenarios
- Implement global error middleware
- Add request logging for debugging
- Include proper error responses with consistent format
- Handle database connection errors gracefully
- Follow the existing error handling patterns
- Ensure proper HTTP status codes
```

## 🔗 Full-Stack Integration Prompting

### Frontend-Backend Communication

#### API Client Implementation
```
Create a comprehensive API client that:
- Extends the existing API client in src/lib/api.js
- Includes proper error handling and retry logic
- Implements request/response interceptors
- Handles authentication tokens automatically
- Provides TypeScript types for all endpoints
- Includes loading state management
- Integrates with Redux for global state updates
```

#### Real-time Features
```
Implement WebSocket integration for real-time vibe updates:
- Add WebSocket support to the ElysiaJS backend
- Create React hooks for WebSocket connection management
- Implement real-time state synchronization with Redux
- Handle connection errors and reconnection logic
- Follow the existing project architecture
- Ensure proper cleanup on component unmount
```

### State Synchronization

#### Optimistic Updates
```
Implement optimistic updates for vibe creation:
- Update Redux state immediately on user action
- Handle API call in the background
- Revert changes if API call fails
- Show appropriate loading and error states
- Maintain data consistency
- Follow the existing Redux patterns
```

## 🎯 Prompt Engineering Best Practices

### Structure Your Requests

#### 1. Context Setting
```
I'm working on the VibeCheck application with:
- Frontend: Next.js 15.4.5, React 19.1.0, TypeScript, Tailwind CSS 4.x
- Backend: Bun runtime, ElysiaJS, PostgreSQL
- State Management: Redux Toolkit
- UI Components: Shadcn/UI

Current task: [Describe what you're trying to accomplish]
```

#### 2. Specific Requirements
```
Requirements:
- Follow the existing project structure in src/
- Use TypeScript for all new code
- Implement proper error handling
- Include responsive design
- Maintain compatibility with existing components
- Follow the established coding patterns
```

#### 3. Constraints and Preferences
```
Constraints:
- Must work with the existing database schema
- Should not break existing API contracts
- Must maintain backward compatibility
- Follow the project's ESLint configuration
```

### Providing Context Effectively

#### File Structure Context
```
The current project structure is:
src/
├── components/
│   ├── ui/           # Shadcn/UI components
│   ├── forms/        # Form components
│   └── layout/       # Layout components
├── pages/            # Next.js pages
├── store/            # Redux store and slices
├── lib/              # Utility functions
└── styles/           # Global styles

I need to add a new component in [specific location]
```

#### Existing Code Context
```
Here's the existing pattern I'm following:

[Include relevant code snippet]

I need to create something similar that [specific requirements]
```

## 📝 Specific Prompt Examples

### Component Creation Examples

#### Creating a Data Table Component
```
Create a data table component for displaying vibes using TanStack Table that:

Context:
- Project uses Next.js with TypeScript and Shadcn/UI
- Data comes from Redux store (vibes slice)
- Should integrate with existing API client

Requirements:
- Sortable columns (date, mood, rating, user)
- Filterable by mood and date range
- Pagination support
- Row actions (edit, delete)
- Responsive design
- Loading and error states
- Export functionality

File location: src/components/tables/VibesTable.tsx

Follow the existing patterns in:
- src/store/slices/vibesSlice.js
- src/lib/api.js
- src/components/ui/ (for Shadcn components)
```

#### Redux Slice Creation
```
Create a Redux Toolkit slice for notifications that:

Context:
- Existing store structure in src/store/
- Should integrate with existing UI slice patterns
- Used for showing success/error messages

Requirements:
- Actions: addNotification, removeNotification, clearAll
- State: array of notifications with id, type, message, timestamp
- Auto-remove notifications after 5 seconds
- Support for different notification types (success, error, warning, info)
- TypeScript interfaces

File location: src/store/slices/notificationsSlice.ts

Follow the pattern established in src/store/slices/uiSlice.js
```

### Backend Development Examples

#### API Endpoint Creation
```
Create a complete API endpoint for user preferences using ElysiaJS:

Context:
- Existing backend structure in ../be/src/
- Database connection pattern in src/db/index.ts
- Controller pattern in src/controllers/
- Route pattern in src/routes/

Requirements:
- CRUD operations for user preferences
- Validation using Elysia's type system
- Integration with existing error handling
- TypeScript interfaces
- Database operations with proper SQL queries
- Follow RESTful conventions

Files to create/modify:
- src/types/preferences.types.ts
- src/controllers/preferences.controller.ts
- src/routes/preferences.ts
- Update src/routes/index.ts

Follow the existing patterns in src/controllers/vibe.controller.ts
```

#### Database Schema Updates
```
Add user preferences table to the existing database schema:

Context:
- Current database initialization in src/db/index.ts
- Existing vibes table structure
- PostgreSQL database

Requirements:
- Table: user_preferences
- Columns: id, user_id, theme, language, notifications_enabled, created_at, updated_at
- Proper constraints and indexes
- Integration with existing schema initialization
- Migration-safe approach

Update the initializeDatabase function in src/db/index.ts
Follow the existing pattern for the vibes table
```

### Debugging and Troubleshooting Examples

#### Error Investigation
```
I'm getting this error in my React component:

[Include error message and stack trace]

Context:
- Component: src/components/forms/VibeEntryForm.jsx
- Using Redux Toolkit and React Hook Form
- Error occurs when submitting the form

Current code:
[Include relevant code snippet]

Help me:
1. Identify the root cause
2. Provide a fix that follows project patterns
3. Suggest improvements to prevent similar issues
4. Ensure the fix maintains TypeScript compatibility
```

#### Performance Optimization
```
The VibesTable component is rendering slowly with large datasets:

Context:
- Component: src/components/tables/VibesTable.tsx
- Using TanStack Table with 1000+ rows
- Redux store contains all vibes data

Current implementation:
[Include relevant code snippet]

Optimize for:
- Faster initial render
- Smooth scrolling
- Efficient filtering/sorting
- Memory usage
- Maintain existing functionality
- Follow React best practices
```

## 🔍 Advanced Prompting Techniques

### Multi-Step Development
```
I need to implement user authentication across the full stack. Break this down into steps:

1. Backend: JWT authentication with ElysiaJS
2. Frontend: Login/logout components with Redux
3. Protected routes implementation
4. Integration testing

For each step, provide:
- Specific file changes needed
- Code examples following project patterns
- Testing recommendations
- Integration points with existing code

Start with step 1: Backend JWT implementation
```

### Code Review Requests
```
Review this component for:
- TypeScript best practices
- React performance optimizations
- Accessibility compliance
- Integration with existing project patterns
- Security considerations

[Include code to review]

Provide specific suggestions with code examples
```

### Refactoring Guidance
```
Refactor the existing vibe creation flow to:
- Improve error handling
- Add optimistic updates
- Enhance user feedback
- Maintain backward compatibility

Current files involved:
- src/components/forms/VibeEntryForm.jsx
- src/store/slices/vibesSlice.js
- src/lib/api.js

Provide step-by-step refactoring plan with code examples
```

## 📚 Best Practices Summary

### Do's ✅
- Always specify the exact file paths and project structure
- Include relevant existing code patterns as examples
- Mention the specific technologies and versions in use
- Request explanations for complex changes
- Ask for TypeScript types and interfaces
- Include error handling requirements
- Specify responsive design needs
- Request integration with existing patterns

### Don'ts ❌
- Don't ask for generic solutions without project context
- Don't ignore existing code patterns and conventions
- Don't request changes that break existing functionality
- Don't forget to mention testing requirements
- Don't overlook accessibility considerations
- Don't ignore performance implications
- Don't skip error handling implementation

### Context Checklist
Before prompting, ensure you include:
- [ ] Current project structure
- [ ] Technology stack and versions
- [ ] Existing code patterns to follow
- [ ] Specific file locations
- [ ] Integration requirements
- [ ] Error handling needs
- [ ] TypeScript requirements
- [ ] Responsive design considerations
- [ ] Performance requirements
- [ ] Testing expectations

This guide should help you create more effective prompts for Augment AI when developing your full-stack applications. The key is to provide rich context about your project structure and requirements while being specific about what you need to accomplish.
