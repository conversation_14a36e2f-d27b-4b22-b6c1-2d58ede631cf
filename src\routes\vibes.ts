import { Elysia, t } from 'elysia';
import { getAllVibes, createVibe, updateVibe, deleteVibe } from '../controllers/vibe.controller.js';

export const vibesRoutes = new Elysia({ prefix: '/vibes' })
  .get('/', getAllVibes)
  .post('/', createVibe, {
    body: t.Object({
      user_name: t.String({ minLength: 1 }),
      mood: t.String(),
      rating: t.Number({ minimum: 1, maximum: 5 }),
      vibe_date: t.String()
    })
  })
  .put('/:id', updateVibe, {
    params: t.Object({
      id: t.String()
    }),
    body: t.Object({
      user_name: t.String({ minLength: 1 }),
      mood: t.String(),
      rating: t.Number({ minimum: 1, maximum: 5 }),
      vibe_date: t.String()
    })
  })
  .delete('/:id', deleteVibe, {
    params: t.Object({
      id: t.String()
    })
  });
