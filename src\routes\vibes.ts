import { Elysia, t } from 'elysia';
import { getAllVibes, createVibe, updateVibe, deleteVibe } from '../controllers/vibe.controller.js';
import { VALID_MOODS } from '../types/vibe.types.js';

// Validation schemas
const VibeBodySchema = t.Object({
  user_name: t.String({
    minLength: 1,
    maxLength: 100,
    description: 'User name (1-100 characters)'
  }),
  mood: t.Union(VALID_MOODS.map(mood => t.Literal(mood)), {
    description: `Mood must be one of: ${VALID_MOODS.join(', ')}`
  }),
  rating: t.Integer({
    minimum: 1,
    maximum: 5,
    description: 'Rating from 1 to 5'
  }),
  vibe_date: t.String({
    pattern: '^\\d{4}-\\d{2}-\\d{2}$',
    description: 'Date in YYYY-MM-DD format'
  })
});

const IdParamSchema = t.Object({
  id: t.String({
    pattern: '^\\d+$',
    description: 'Numeric ID'
  })
});

export const vibesRoutes = new Elysia({ prefix: '/vibes' })
  .get('/', getAllVibes, {
    detail: {
      summary: 'Get all vibes',
      description: 'Retrieve all vibes ordered by creation date (newest first)',
      tags: ['Vibes']
    }
  })
  .post('/', createVibe, {
    body: VibeBodySchema,
    detail: {
      summary: 'Create a new vibe',
      description: 'Create a new vibe entry',
      tags: ['Vibes']
    }
  })
  .put('/:id', updateVibe, {
    params: IdParamSchema,
    body: VibeBodySchema,
    detail: {
      summary: 'Update a vibe',
      description: 'Update an existing vibe by ID',
      tags: ['Vibes']
    }
  })
  .delete('/:id', deleteVibe, {
    params: IdParamSchema,
    detail: {
      summary: 'Delete a vibe',
      description: 'Delete a vibe by ID',
      tags: ['Vibes']
    }
  });
