# VibeCheck Backend API

A modern backend API built with Bun runtime and ElysiaJS framework for the VibeCheck application.

## 🚀 Features

- **Fast Runtime**: Built with Bun for superior performance
- **Modern Framework**: ElysiaJS with TypeScript support
- **Database**: PostgreSQL with connection pooling
- **CORS**: Configured for frontend integration
- **Validation**: Request/response validation with Elysia
- **Error Handling**: Comprehensive error handling and logging

## 📋 Prerequisites

- [Bun](https://bun.sh/) runtime installed
- [PostgreSQL](https://www.postgresql.org/) database running
- Database named `vibecheck` created

## 🛠 Installation

1. **Install dependencies:**
   ```bash
   bun install
   ```

2. **Setup environment:**
   ```bash
   cp .env.example .env
   # Edit .env with your database credentials
   ```

3. **Setup database:**
   ```bash
   # Create database (if not exists)
   createdb vibecheck

   # Run schema
   bun run db:setup
   ```

## 🏃‍♂️ Running the Application

### Development Mode
```bash
bun run dev
```

### Production Mode
```bash
bun run start
```

The API will be available at `http://localhost:3001`

## 📚 API Endpoints

### Health Check
- `GET /api/health` - API health status

### Vibes CRUD
- `GET /api/vibes` - Get all vibes
- `POST /api/vibes` - Create new vibe
- `PUT /api/vibes/:id` - Update vibe
- `DELETE /api/vibes/:id` - Delete vibe

### Example Request
```bash
# Create a new vibe
curl -X POST http://localhost:3001/api/vibes \
  -H "Content-Type: application/json" \
  -d '{
    "user_name": "John Doe",
    "mood": "Happy",
    "rating": 5,
    "vibe_date": "2024-01-15"
  }'
```

## 🗂 Project Structure

```
src/
├── controllers/        # Business logic
├── db/                # Database connection and schema
├── middleware/        # Custom middleware
├── routes/           # API route definitions
├── types/            # TypeScript type definitions
└── index.ts          # Application entry point
```

## 🧪 Testing

```bash
bun test
```

## 📝 Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `DATABASE_URL` | PostgreSQL connection string | Required |
| `PORT` | Server port | 3001 |
| `NODE_ENV` | Environment mode | development |
| `FRONTEND_URL` | Frontend URL for CORS | http://localhost:3000 |

## 🚀 Deployment

### Using Docker
```bash
# Build image
docker build -t vibecheck-api .

# Run container
docker run -p 3001:3001 vibecheck-api
```

### Manual Deployment
```bash
# Build for production
bun run build

# Start production server
bun run start
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

MIT License - see LICENSE file for details
