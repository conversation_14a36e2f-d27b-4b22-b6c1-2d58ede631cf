import postgres from 'postgres';
import 'dotenv/config';

const connectionString = process.env.DATABASE_URL || 'postgresql://postgres:postgres123@localhost:5432/postgres';

// Configure postgres connection with proper settings
const sql = postgres(connectionString, {
  max: parseInt(process.env.DB_MAX_CONNECTIONS || '20'),
  idle_timeout: parseInt(process.env.DB_IDLE_TIMEOUT || '20'),
  connect_timeout: parseInt(process.env.DB_CONNECT_TIMEOUT || '10'),
  onnotice: () => {}, // Suppress notices
});

// Test database connection
export async function testConnection() {
  try {
    await sql`SELECT 1 as test`;
    console.log('✅ Database connected successfully');
    return true;
  } catch (error) {
    console.error('❌ Database connection failed:', error);
    return false;
  }
}

// Initialize database schema
export async function initializeDatabase() {
  try {
    // Create vibes table if it doesn't exist
    await sql`
      CREATE TABLE IF NOT EXISTS vibes (
        id SERIAL PRIMARY KEY,
        user_name TEXT NOT NULL CHECK (length(user_name) > 0),
        mood VARCHAR(50) NOT NULL CHECK (mood IN ('Happy', 'Okay', 'Sad', 'Excited', 'Anxious')),
        rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
        vibe_date DATE NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
      )
    `;

    // Create indexes for better query performance
    await sql`CREATE INDEX IF NOT EXISTS idx_vibes_date ON vibes(vibe_date)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_vibes_mood ON vibes(mood)`;
    await sql`CREATE INDEX IF NOT EXISTS idx_vibes_created_at ON vibes(created_at)`;

    // Create trigger function for updated_at
    await sql`
      CREATE OR REPLACE FUNCTION update_updated_at_column()
      RETURNS TRIGGER AS $$
      BEGIN
          NEW.updated_at = CURRENT_TIMESTAMP;
          RETURN NEW;
      END;
      $$ language 'plpgsql'
    `;

    // Drop trigger if exists
    await sql`DROP TRIGGER IF EXISTS update_vibes_updated_at ON vibes`;

    // Create trigger
    await sql`
      CREATE TRIGGER update_vibes_updated_at
          BEFORE UPDATE ON vibes
          FOR EACH ROW
          EXECUTE FUNCTION update_updated_at_column()
    `;

    console.log('✅ Database schema initialized successfully');
    return true;
  } catch (error) {
    console.error('❌ Database schema initialization failed:', error);
    return false;
  }
}

export default sql;
