{"name": "vibecheck-backend", "version": "1.0.0", "description": "VibeCheck Backend API built with Bun and ElysiaJS", "main": "src/index.ts", "module": "src/index.ts", "type": "module", "private": true, "scripts": {"dev": "bun run --watch src/index.ts", "start": "bun run src/index.ts", "build": "bun build src/index.ts --outdir ./dist", "test": "bun test", "test:watch": "bun test --watch", "clean": "rm -rf dist node_modules/.cache"}, "keywords": ["bun", "elysia", "postgresql", "api", "crud", "vibecheck"], "author": "VibeCheck Team", "license": "MIT", "devDependencies": {"@types/bun": "^1.2.19"}, "peerDependencies": {"typescript": "^5"}, "dependencies": {"@elysiajs/cors": "^1.3.3", "dotenv": "^17.2.1", "elysia": "^1.3.8", "postgres": "^3.4.7"}}